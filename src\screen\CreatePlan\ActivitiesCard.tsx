'use client';

import { <PERSON><PERSON>, <PERSON>, CardBody, Image } from '@heroui/react';

export default function ActivitiesCard() {
  const data = [
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/aec23adcb534d851fa057451a74e10aeba707340.jpg',
      heading: 'The Golden Sands ',
      description:
        'Lorem ipsum dolor sit amet, con turadipiscingelit. In sed et donec purus viverra. Sitjusto velit, eu sed',
    },
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/0e72dd416b5d0c589ca141595fa00cc63165cccc.jpg',
      heading: 'Exploring the Enchanted',
      description:
        'Experience the tranquility of towering trees and hidden waterfalls. A journey that centers on mindfulness and appreciation of nature.',
    },
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/88cda908218c10604b28f79baf31e5ab5ee8b91b.jpg',
      heading: 'Culinary Delights',
      description:
        'Dive into the rich flavors of Creole and Cajun cuisine, as you savor dishes that tell the story of the city`s vibrant culture.',
    },
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/205a510999c9daa5a89cf8a953c78c97937f1280.jpg',
      heading: 'The Historic Streets of Boston',
      description:
        'Walk through history with a blend of modern attractions and historic landmarks, capturing the essence of America`s revolutionary past.',
    },
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/656937a4f9682ba78f7251a554668191f2f307c7.jpg',
      heading: 'The Urban Wonders of Tokyo',
      description:
        'Navigate the bustling streets and serene temples, discovering the perfect balance between tradition and cutting-edge technology.',
    },
    {
      img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/cfca8d60f6aac2b71b2819575b69378043ca0a93.jpg',
      heading: 'The Serene Mountains of Colorado',
      description:
        'Challenge yourself with thrilling outdoor activities amidst breathtaking alpine scenery, perfect for adrenaline seekers.',
    },
  ];

  return (
    <div className="grid grid-cols-3 gap-4 max-md:grid-cols-1">
      {data.map(item => (
        <Card
          key={item.heading}
          isPressable
          shadow="sm"
          onPress={() => {}}
          className="shadow-none rounded-md w-full"
        >
          <CardBody className="relative overflow-visible p-0">
            {/* Gradient Overlay */}
            {/* <div className="absolute top-0 left-0 w-full h-full z-10 bg-[linear-gradient(180deg,#F29C72_0%,rgba(255,255,255,0)_100%)] pointer-events-none rounded-sm" /> */}

            {/* Image */}
            <Image
              alt={item.img}
              className="min-w-full object-cover h-[320px] shadow-none"
              radius="sm"
              src={item.img}
              width="100%"
            />

            <div className="p-3 absolute bottom-[56px] z-[99999999999999999999999999999999] bg-black/70 text-white">
              <p className="text-base font-bold text-start mb-2">
                {item.heading}
              </p>
              <p className=" text-sm mt-1 leading-6.5 items-end">
                {item.description}
              </p>
            </div>
            <div className="p-3 absolute bottom-0 z-[99999999999999999999999999999999] bg-black text-white flex flex-row justify-between items-center w-full rounded-bl-sm rounded-br-sm">
              <p className="text-base font-bold text-start mb-2">
                $165.3 /night
              </p>
              <Button color="primary" variant="flat" size='sm' className='bg-[#E9E7FB] rounded-full'>
                Flat
              </Button>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
}
