import ChatSkeleton from '@/components/loaders/ChatSkeleton';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import PrimaryFilterSkeleton from '@/components/loaders/PrimaryFilterSkeleton';

import RecentSearches from './RecentSearches';
import Image from 'next/image';
import ExploreComp from './Explore';
import ChatWithShasa from '@/components/globalComponents/ChatWithShasa';

interface DiscoverPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}
const Explore = ({
  isLoading = false,
  loadingComponents = {},
}: DiscoverPageProps) => {
  // Use the loading states directly from props
  const componentLoading = {
    filter: loadingComponents.filter ?? isLoading,
    chat: loadingComponents.chat ?? isLoading,
    recommendations: loadingComponents.recommendations ?? isLoading,
  };
  return (
    <div className=" md:h-[calc(100vh-100px)] p-3 sm:p-5">
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-3 sm:gap-4 flex-1">
        {/* Chat Section */}
        <div className="lg:col-span-2 order-2 lg:order-1  md:h-[calc(100vh-95px)]  md:overflow-y-scroll">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
          <div className="mt-4">
            <p className="text-xl font-bold mb-3">map</p>
            <Image
              src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/map.svg"
              alt="map"
              width={200}
              height={300}
              className="w-full h-[350px] object-cover rounded-xl"
            />
          </div>
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-4 order-1 lg:order-2  md:h-[calc(100vh-95px)]  md:overflow-y-auto md:overflow-x-hidden">
          {/* Primary Filter Section */}
          <div className="">
            {componentLoading.filter ? (
              <PrimaryFilterSkeleton />
            ) : (
              <PrimaryFilter />
            )}
          </div>
          <ExploreComp />
          <RecentSearches />
        </div>
      </div>
    </div>
  );
};

export default Explore;
