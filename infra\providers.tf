terraform {
  backend "gcs" {
    bucket  = "nxvoy-terraform-states"
    prefix  = "nxvoy/travel-agent-fer2/"
  } 

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.36"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.17"
    }       
  }   
}

provider "google" {
  credentials = file("terraform_service_account.json")
  project     = var.project_id
  region      = "europe-west2"
}

# Fetch GKE cluster info
data "google_container_cluster" "primary" {
  name     = data.terraform_remote_state.gcp.outputs.cluster_name
  location = "europe-west2"
}

# Required for Kubernetes+GKE authentication
data "google_client_config" "default" {}

# Configure Kubernetes provider using GKE credential
provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.endpoint}"
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth[0].cluster_ca_certificate)
  token                  = data.google_client_config.default.access_token
}

# Configure Helm provider (uses same config as Kubernetes provider by default)
provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.endpoint}"
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth[0].cluster_ca_certificate)
    token                  = data.google_client_config.default.access_token
  }
}