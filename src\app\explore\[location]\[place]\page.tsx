'use client';

import { useState, useEffect } from 'react';

import ExploreDetailsPage from '@/screen/ExploreDetails';

export default function ExploreDetails() {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading time - replace with actual data fetching logic
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // Show skeleton for 2 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <div>
      <ExploreDetailsPage isLoading={isLoading} />
    </div>
  );
}
