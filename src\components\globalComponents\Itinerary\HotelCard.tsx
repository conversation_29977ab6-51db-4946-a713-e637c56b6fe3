'use client';

import React, { useState } from 'react';
import { MdHotel } from 'react-icons/md';
import { IoIosArrowUp } from 'react-icons/io';
import type { Flight } from '@/types/flight';
import { Button, Divider } from '@heroui/react';

type Props = {
  flight: Flight;
};

export default function Hotelcard({ flight }: Props) {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className="w-full mx-auto overflow-hidden  rounded-lg">
      <div className="flex flex-row gap-4">
        <div className='min-w-[78px] text-center'>
          {!isOpen ? (
            <Button
              color="primary"
              variant="flat"
              size="md"
              onPress={() => setIsOpen(!isOpen)}
              className="text-lg text-subtitle "
            >
              +
            </Button>
          ) : (
            <p className=" font-medium text-base mt-1">10:00</p>
          )}
        </div>

        <div className="w-full">
            <div className="flex flex-row  items-center">
            {isOpen ? (
              <>
            <div className="w-2 h-2 min-w-2 max-w-2 rounded-full bg-primary-200 mr-2 -ml-4"></div>
            <div className="flex h-7 items-center  text-small">
              <Divider orientation="vertical"  className='w-1 bg-black'/>
            </div>
            </>
            ) : null}
          {/* Header */}
          <div
            className={`flex flex-row w-full items-center ${
              !isOpen
                ? 'border border-gray-300 rounded-lg'
                : ' '
            }`}
          >
            <div className="w-full">
              <div className="flex items-center w-full text-sm px-4 py-2">
                <MdHotel className="z-10 text-black " size={20} />
                <span className="text-subtitle mr-3 ml-3 font-bold text-sm">
                  Hotel
                </span>
                <span className="truncate text-base text-subtitle font-medium ">
                  {flight.from} to {flight.to}
                </span>
                <span className="mx-2 text-gray-400">|</span>
                <span className="text-base text-black">{flight.duration}</span>
                {isOpen && (
                  <div
                    onClick={() => setIsOpen(!isOpen)}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        setIsOpen(!isOpen);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    className=" cursor-pointer"
                  >
                    <IoIosArrowUp className="z-10 text-black ml-2" size={20} />
                  </div>
                )}
                {isOpen && (
                  <div className="ml-auto flex gap-4 text-xs">
                    <button
                      type="button"
                      className="text-primary-200 hover:underline"
                    >
                      Remove
                    </button>
                    <button
                      type="button"
                      className="text-primary-200 hover:underline"
                    >
                      Change
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          </div>

          {/* Main Content with smooth transition */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex flex-col md:flex-row justify-between items-center px-4 py-3 text-sm">
              {/* Airline Logos */}
              <div className="flex flex-row gap-3 items-center">
                <div className="flex flex-col gap-3 justify-center md:justify-start border border-[#C4C4FF] aspect-square w-[80px] h-[80px] rounded-lg">
                  {flight.airlines.map(airline => (
                    <img
                      key={airline.name}
                      src={airline.logo}
                      alt={airline.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ))}
                </div>
                <div className="md:w-[150px]">
                  <p className="text-black font-semibold">
                    History, Museum, Explore, Art
                  </p>
                  <p className="text-default-700">Atlantic Aviasion ABQ</p>
                </div>
              </div>

              {/* Departure */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.departure.time}
                </p>
                <p className="text-default-700">{flight.departure.location}</p>
                <p className="text-default-700">{flight.departure.date}</p>
              </div>

              {/* Flight Path */}
              <div className="w-[120px] flex flex-col items-center text-default-700">
                <div className="relative w-full h-6 flex items-center justify-center">
                  <div className="w-full border-t border-default-400 rotate-90 md:rotate-0" />
                </div>
              </div>

              {/* Arrival */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.arrival.time}
                </p>
                <p className="text-default-700">{flight.arrival.location}</p>
                <p className="text-default-700">{flight.arrival.date}</p>
              </div>

              {/* Baggage */}
              <div className="text-left text-default-700 pl-4 border-l">
                <p>Breakfast included</p>
                <p>Skyline Suites</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
